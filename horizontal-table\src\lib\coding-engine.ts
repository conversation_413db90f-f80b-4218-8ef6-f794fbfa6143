/**
 * 横向层级表格编码引擎
 * 实现A001系列递进式编码的生成和管理
 */

import { TableRow, Level, CodeSegment, ValidationResult, ERROR_CODES, TableError } from '@/types/table';

export class CodingEngine {
  private static readonly BASE_PREFIX = 'A';
  private static readonly SEGMENT_LENGTH = 3;
  
  /**
   * 根据层级获取编码段数
   * LV1: 1段 (A001)
   * LV2: 2段 (A001001) 
   * LV3: 3段 (A001001001)
   * LV4: 4段 (A001001001001)
   * LV5: 5段 (A001001001001001)
   */
  static getSegmentCountByLevel(level: Level): number {
    const levelMap: Record<Level, number> = {
      'LV1': 1,
      'LV2': 2,
      'LV3': 3,
      'LV4': 4,
      'LV5': 5
    };
    return levelMap[level];
  }
  
  /**
   * 根据编码段数获取层级
   */
  static getLevelBySegmentCount(segmentCount: number): Level {
    const segmentMap: Record<number, Level> = {
      1: 'LV1',
      2: 'LV2', 
      3: 'LV3',
      4: 'LV4',
      5: 'LV5'
    };
    return segmentMap[segmentCount] || 'LV1';
  }
  
  /**
   * 解析编码为段结构
   * A001 -> {prefix: 'A', segments: [1]}
   * A001001 -> {prefix: 'A', segments: [1, 1]}
   */
  static parseCode(code: string): CodeSegment {
    if (!code.startsWith(this.BASE_PREFIX)) {
      throw new TableError('Invalid code format', 'INVALID_CODE', { code });
    }
    
    const numberPart = code.substring(1);
    const segments: number[] = [];
    
    for (let i = 0; i < numberPart.length; i += this.SEGMENT_LENGTH) {
      const segment = numberPart.substring(i, i + this.SEGMENT_LENGTH);
      segments.push(parseInt(segment, 10));
    }
    
    return {
      prefix: this.BASE_PREFIX,
      segments
    };
  }
  
  /**
   * 根据段数组构建编码
   * [1, 2, 3] -> A001002003
   */
  static buildCode(segments: number[]): string {
    const numberPart = segments
      .map(segment => String(segment).padStart(this.SEGMENT_LENGTH, '0'))
      .join('');
    return `${this.BASE_PREFIX}${numberPart}`;
  }
  
  /**
   * 生成新行的编码
   * 根据需求文档：新增行比当前行少一个单元格内容，编码少一节，最后一节编码+1
   */
  static generateNewRowCode(currentRowCode: string, allRows: TableRow[]): string {
    const currentSegment = this.parseCode(currentRowCode);
    
    // 新行编码段数 = 当前行编码段数 - 1
    const newSegmentCount = Math.max(1, currentSegment.segments.length - 1);
    
    if (newSegmentCount === 1) {
      // 生成LV1编码：找到最大的LV1编码+1
      const lv1Codes = allRows
        .filter(row => this.getLevelBySegmentCount(this.parseCode(row.code).segments.length) === 'LV1')
        .map(row => this.parseCode(row.code).segments[0])
        .sort((a, b) => b - a);
      
      const nextNumber = lv1Codes.length > 0 ? lv1Codes[0] + 1 : 1;
      return `${this.BASE_PREFIX}${String(nextNumber).padStart(this.SEGMENT_LENGTH, '0')}`;
    } else {
      // 生成多段编码：取当前编码的前n-1段，最后一段+1
      const parentSegments = currentSegment.segments.slice(0, newSegmentCount - 1);
      const lastSegment = currentSegment.segments[newSegmentCount - 1] + 1;
      
      const newSegments = [...parentSegments, lastSegment];
      return this.buildCode(newSegments);
    }
  }
  
  /**
   * 生成编码 - 兼容方法
   * @param parentCode 父级编码 (可选)
   * @param level 目标层级
   * @param position 在同级中的位置 (从1开始)
   */
  static generateCode(parentCode: string | null, level: Level, position: number): string {
    const targetSegmentCount = this.getSegmentCountByLevel(level);

    if (level === 'LV1') {
      // 根级编码: A001, A002, A003...
      return this.buildCode([position]);
    }

    if (!parentCode) {
      throw new TableError('Parent code required for non-root level', 'INVALID_OPERATION');
    }

    const parentSegment = this.parseCode(parentCode);
    const parentSegmentCount = parentSegment.segments.length;

    // 验证层级关系
    if (targetSegmentCount !== parentSegmentCount + 1) {
      throw new TableError(
        `Invalid level transition: parent has ${parentSegmentCount} segments, target level requires ${targetSegmentCount}`,
        'INVALID_LEVEL'
      );
    }

    // 构建新编码
    return this.buildCode([...parentSegment.segments, position]);
  }
  
  /**
   * 层级上调编码生成
   * 规则：编码增加一节，内容继承上级
   */
  static generateLevelUpCode(currentCode: string, allRows: TableRow[]): string {
    const currentSegment = this.parseCode(currentCode);
    
    // 在同层级中找到下一个可用位置
    const newSegments = [...currentSegment.segments, 1];
    
    // 检查是否有冲突，如果有则递增
    let position = 1;
    while (allRows.some(row => row.code === this.buildCode([...currentSegment.segments, position]))) {
      position++;
    }
    
    return this.buildCode([...currentSegment.segments, position]);
  }
  
  /**
   * 层级下调编码生成
   * 规则：编码减少一节，排在同层级最后
   */
  static generateLevelDownCode(currentCode: string, allRows: TableRow[]): string {
    const currentSegment = this.parseCode(currentCode);
    
    if (currentSegment.segments.length <= 1) {
      throw new TableError('Cannot demote LV1 level', 'MIN_LEVEL_EXCEEDED');
    }
    
    // 减少一节
    const newSegments = currentSegment.segments.slice(0, -1);
    const parentPrefix = this.buildCode(newSegments.slice(0, -1));
    
    // 找到同层级的最大编码
    const sameLevelCodes = allRows
      .filter(row => {
        const rowSegment = this.parseCode(row.code);
        return rowSegment.segments.length === newSegments.length &&
               (newSegments.length === 1 || 
                this.buildCode(rowSegment.segments.slice(0, -1)) === parentPrefix);
      })
      .map(row => this.parseCode(row.code).segments[newSegments.length - 1])
      .sort((a, b) => b - a);
    
    const nextPosition = sameLevelCodes.length > 0 ? sameLevelCodes[0] + 1 : 1;
    newSegments[newSegments.length - 1] = nextPosition;
    
    return this.buildCode(newSegments);
  }
  
  /**
   * 获取编码的最后一段数字
   * 例如: "A001002003" -> 3
   */
  static getLastSegment(code: string): number {
    const segment = this.parseCode(code);
    return segment.segments[segment.segments.length - 1] || 0;
  }
  
  /**
   * 获取父级编码
   * 例如: "A001002003" -> "A001002"
   */
  static getParentCode(code: string): string | null {
    const segment = this.parseCode(code);
    if (segment.segments.length <= 1) {
      return null; // 根级没有父级
    }

    return this.buildCode(segment.segments.slice(0, -1));
  }
  
  /**
   * 验证编码格式
   */
  static validateCode(code: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // 基本格式检查
      if (!code.startsWith(this.BASE_PREFIX)) {
        errors.push(`Code must start with '${this.BASE_PREFIX}'`);
      }

      const numberPart = code.slice(1);
      if (numberPart.length % this.SEGMENT_LENGTH !== 0) {
        errors.push(`Code segments must be ${this.SEGMENT_LENGTH} digits each`);
      }

      // 解析检查
      const segment = this.parseCode(code);
      
      // 检查段数是否合理
      if (segment.segments.length > 5) {
        errors.push('Code cannot have more than 5 segments (max level LV5)');
      }

      // 检查每段是否为正数
      segment.segments.forEach((seg, index) => {
        if (seg <= 0) {
          errors.push(`Segment ${index + 1} must be a positive number`);
        }
      });

    } catch (error) {
      errors.push(`Invalid code format: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}

// 导出默认类
export default CodingEngine;
