/**
 * 层级标签组件
 * 显示层级标识，支持不同层级的颜色区分
 */

'use client';

import React from 'react';
import { Level } from '@/types/table';

interface LevelBadgeProps {
  level: Level;
  size?: 'sm' | 'md' | 'lg';
}

export function LevelBadge({ level, size = 'sm' }: LevelBadgeProps) {
  // 获取层级样式
  const getLevelStyle = (level: Level) => {
    const styleMap: Record<Level, string> = {
      'LV1': 'bg-blue-100 text-blue-800 border-blue-200',
      'LV2': 'bg-green-100 text-green-800 border-green-200',
      'LV3': 'bg-purple-100 text-purple-800 border-purple-200',
      'LV4': 'bg-orange-100 text-orange-800 border-orange-200',
      'LV5': 'bg-red-100 text-red-800 border-red-200'
    };
    return styleMap[level] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  // 获取尺寸样式
  const getSizeStyle = (size: 'sm' | 'md' | 'lg') => {
    const sizeMap = {
      'sm': 'px-2 py-1 text-xs',
      'md': 'px-3 py-1 text-sm',
      'lg': 'px-4 py-2 text-base'
    };
    return sizeMap[size];
  };

  return (
    <span 
      className={`
        inline-flex items-center font-medium rounded border
        ${getLevelStyle(level)}
        ${getSizeStyle(size)}
      `}
      data-testid="level-badge"
      data-level={level}
    >
      {level}
    </span>
  );
}

// 默认导出
export default LevelBadge;
