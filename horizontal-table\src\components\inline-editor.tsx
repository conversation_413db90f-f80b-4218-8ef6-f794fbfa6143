/**
 * 内联编辑器组件
 * 支持单元格的内联编辑功能
 */

'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';

interface InlineEditorProps {
  value: string;
  placeholder?: string;
  onChange: (value: string) => void;
  className?: string;
  disabled?: boolean;
}

export function InlineEditor({
  value,
  placeholder = '',
  onChange,
  className = '',
  disabled = false
}: InlineEditorProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);

  // 同步外部值变化
  useEffect(() => {
    setEditValue(value);
  }, [value]);

  // 开始编辑
  const startEditing = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    if (disabled) return;
    setIsEditing(true);
    setEditValue(value);
  }, [disabled, value]);

  // 完成编辑
  const finishEditing = useCallback(() => {
    setIsEditing(false);
    if (editValue !== value) {
      onChange(editValue);
    }
  }, [editValue, value, onChange]);

  // 取消编辑
  const cancelEditing = useCallback(() => {
    setIsEditing(false);
    setEditValue(value);
  }, [value]);

  // 处理键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      finishEditing();
    } else if (e.key === 'Escape') {
      cancelEditing();
    }
  }, [finishEditing, cancelEditing]);

  // 自动聚焦
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  if (isEditing) {
    return (
      <input
        ref={inputRef}
        type="text"
        value={editValue}
        onChange={(e) => setEditValue(e.target.value)}
        onBlur={finishEditing}
        onKeyDown={handleKeyDown}
        className={`
          w-full px-2 py-1 text-sm border border-blue-500 rounded
          focus:outline-none focus:ring-1 focus:ring-blue-500
          ${className}
        `}
        data-testid="inline-editor-input"
      />
    );
  }

  return (
    <div
      onClick={startEditing}
      className={`
        w-full px-2 py-1 text-sm rounded cursor-text min-h-[28px] flex items-center
        ${disabled 
          ? 'bg-gray-50 text-gray-400 cursor-not-allowed' 
          : 'hover:bg-gray-50 transition-colors'
        }
        ${!value && !disabled ? 'text-gray-400' : ''}
        ${className}
      `}
      data-testid="inline-editor-display"
    >
      {value || placeholder}
    </div>
  );
}

// 默认导出
export default InlineEditor;
