/**
 * 横向层级表格系统类型定义
 * 基于正确需求理解设计的数据结构
 */

// 层级类型 - 支持LV1到LV5
export type Level = 'LV1' | 'LV2' | 'LV3' | 'LV4' | 'LV5';

// 表格行数据 - 横向展示的层级数据
export interface TableRow {
  id: string;                    // 唯一标识
  code: string;                  // A001, A001001, A001001001 等层级编码
  level: Level;                  // 层级：LV1-LV5
  
  // 标题字段 - 根据层级启用
  title1?: string;               // 标题1 - 所有层级都可填
  title2?: string;               // 标题2 - LV2+可填
  title3?: string;               // 标题3 - LV3+可填
  title4?: string;               // 标题4 - 动态列，LV4+可填
  title5?: string;               // 标题5 - 动态列，LV5+可填
  description?: string;          // 细节描述 - 最高层级可填
  
  // 层级关系
  parentId?: string;             // 父级行ID
  children: string[];            // 子级行ID数组
  position: number;              // 同层级内的排序位置
  
  // 状态信息
  isExpanded: boolean;           // 是否展开子级
  createdAt: number;             // 创建时间戳
  updatedAt: number;             // 更新时间戳
}

// 字段启用状态 - 根据层级自动计算
export interface FieldEnabledState {
  title1: boolean;               // 始终为true
  title2: boolean;               // LV2+为true
  title3: boolean;               // LV3+为true
  title4: boolean;               // LV4+为true（如果存在动态列）
  title5: boolean;               // LV5+为true（如果存在动态列）
  description: boolean;          // 最高层级为true
}

// 动态列定义
export interface DynamicColumn {
  key: string;                   // 列键名，如 'title4', 'title5'
  title: string;                 // 列标题
  width: number;                 // 列宽度
  resizable: boolean;            // 是否可调整大小
  sortable: boolean;             // 是否可排序
  order: number;                 // 显示顺序
  createdAt: number;             // 创建时间
}

// 编码段结构
export interface CodeSegment {
  prefix: string;                // 前缀，通常是 'A'
  segments: number[];            // 编码段数组，如 [1, 2, 3] 表示 A001002003
}

// 操作类型
export type OperationType = 
  | 'ADD_ROW'
  | 'DELETE_ROW'
  | 'LEVEL_UP'
  | 'LEVEL_DOWN'
  | 'MOVE_ROW'
  | 'UPDATE_CELL'
  | 'ADD_COLUMN'
  | 'DELETE_COLUMN';

// 操作参数
export interface OperationParams {
  ADD_ROW: {
    insertAfter?: string;        // 在指定行后插入
    level?: Level;               // 指定层级
  };
  DELETE_ROW: {
    rowId: string;               // 要删除的行ID
  };
  LEVEL_UP: {
    rowId: string;               // 要调整的行ID
  };
  LEVEL_DOWN: {
    rowId: string;               // 要调整的行ID
  };
  MOVE_ROW: {
    rowId: string;               // 要移动的行ID
    targetPosition: number;      // 目标位置
  };
  UPDATE_CELL: {
    rowId: string;               // 行ID
    field: keyof TableRow;       // 字段名
    value: string;               // 新值
  };
  ADD_COLUMN: {
    title?: string;              // 列标题
    afterColumn?: string;        // 在指定列后插入
  };
  DELETE_COLUMN: {
    columnKey: string;           // 要删除的列键
  };
}

// 验证结果
export interface ValidationResult {
  isValid: boolean;              // 是否有效
  errors: string[];              // 错误信息
  warnings: string[];            // 警告信息
}

// 表格状态
export interface TableState {
  rows: Record<string, TableRow>; // 所有行数据
  rootIds: string[];             // 根级行ID列表
  selectedIds: string[];         // 选中的行ID列表
  editingCell: {                 // 正在编辑的单元格
    rowId: string;
    field: keyof TableRow;
  } | null;
  dynamicColumns: DynamicColumn[]; // 动态列配置
  expandedIds: Set<string>;      // 展开的行ID集合
  lastOperation: {               // 最后一次操作
    type: OperationType;
    timestamp: number;
    params: any;
  } | null;
}

// 错误代码
export const ERROR_CODES = {
  INVALID_CODE: 'INVALID_CODE',
  INVALID_LEVEL: 'INVALID_LEVEL',
  INVALID_OPERATION: 'INVALID_OPERATION',
  ROW_NOT_FOUND: 'ROW_NOT_FOUND',
  PARENT_NOT_FOUND: 'PARENT_NOT_FOUND',
  CIRCULAR_REFERENCE: 'CIRCULAR_REFERENCE',
  MAX_LEVEL_EXCEEDED: 'MAX_LEVEL_EXCEEDED',
  MIN_LEVEL_EXCEEDED: 'MIN_LEVEL_EXCEEDED',
  DUPLICATE_CODE: 'DUPLICATE_CODE',
  INVALID_POSITION: 'INVALID_POSITION'
} as const;

// 表格错误类
export class TableError extends Error {
  constructor(
    message: string,
    public code: keyof typeof ERROR_CODES,
    public context?: any
  ) {
    super(message);
    this.name = 'TableError';
  }
}
