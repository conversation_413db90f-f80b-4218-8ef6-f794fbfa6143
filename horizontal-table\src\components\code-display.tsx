/**
 * 编码显示组件
 * 显示层级编码，支持视觉层次
 */

'use client';

import React from 'react';
import { Level } from '@/types/table';

interface CodeDisplayProps {
  code: string;
  level: Level;
}

export function CodeDisplay({ code, level }: CodeDisplayProps) {
  // 获取层级颜色
  const getLevelColor = (level: Level): string => {
    const colorMap: Record<Level, string> = {
      'LV1': 'text-blue-600',
      'LV2': 'text-green-600', 
      'LV3': 'text-purple-600',
      'LV4': 'text-orange-600',
      'LV5': 'text-red-600'
    };
    return colorMap[level] || 'text-gray-600';
  };

  return (
    <div 
      className="flex items-center"
      data-testid="code-display"
    >
      {/* 编码文本 */}
      <span 
        className={`font-mono text-sm font-medium ${getLevelColor(level)}`}
        data-testid="code-text"
      >
        {code}
      </span>
    </div>
  );
}

// 默认导出
export default CodeDisplay;
