/**
 * 横向层级表格状态管理
 * 使用Zustand管理表格状态和操作
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  TableRow, 
  TableState, 
  Level, 
  OperationType, 
  OperationParams, 
  DynamicColumn,
  FieldEnabledState 
} from '@/types/table';
import { CodingEngine } from '@/lib/coding-engine';

interface TableStore extends TableState {
  // 基础操作
  addRow: (params: OperationParams['ADD_ROW']) => void;
  deleteRow: (params: OperationParams['DELETE_ROW']) => void;
  updateCell: (params: OperationParams['UPDATE_CELL']) => void;
  
  // 层级操作
  levelUp: (params: OperationParams['LEVEL_UP']) => void;
  levelDown: (params: OperationParams['LEVEL_DOWN']) => void;
  
  // 动态列操作
  addColumn: (params: OperationParams['ADD_COLUMN']) => void;
  deleteColumn: (params: OperationParams['DELETE_COLUMN']) => void;
  
  // 选择操作
  selectRow: (rowId: string) => void;
  selectMultiple: (rowIds: string[]) => void;
  clearSelection: () => void;
  
  // 编辑操作
  startEdit: (rowId: string, field: keyof TableRow) => void;
  stopEdit: () => void;
  
  // 工具方法
  getFieldEnabledState: (level: Level, hasTitle4: boolean, hasTitle5: boolean) => FieldEnabledState;
  getMaxLevel: () => Level;
  initializeWithSampleData: () => void;
}

export const useTableStore = create<TableStore>()(
  devtools(
    (set, get) => ({
      // 初始状态
      rows: {},
      rootIds: [],
      selectedIds: [],
      editingCell: null,
      dynamicColumns: [],
      expandedIds: new Set(),
      lastOperation: null,

      // 添加行
      addRow: ({ insertAfter, level = 'LV1' }) => {
        set((state) => {
          const newId = `row_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          const allRows = Object.values(state.rows);
          
          let newCode: string;
          let newPosition: number;
          
          if (insertAfter && state.rows[insertAfter]) {
            // 在指定行后添加
            newCode = CodingEngine.generateNewRowCode(state.rows[insertAfter].code, allRows);
            newPosition = state.rows[insertAfter].position + 1;
          } else {
            // 添加到末尾
            const maxPosition = allRows.length > 0 
              ? Math.max(...allRows.filter(r => r.level === level).map(r => r.position))
              : 0;
            newPosition = maxPosition + 1;
            newCode = CodingEngine.generateCode(null, level, newPosition);
          }
          
          const newRow: TableRow = {
            id: newId,
            code: newCode,
            level,
            title1: `新项目 ${newPosition}`,
            parentId: undefined,
            children: [],
            position: newPosition,
            isExpanded: true,
            createdAt: Date.now(),
            updatedAt: Date.now()
          };
          
          const newRows = { ...state.rows, [newId]: newRow };
          const newRootIds = level === 'LV1' ? [...state.rootIds, newId] : state.rootIds;
          
          return {
            rows: newRows,
            rootIds: newRootIds,
            lastOperation: {
              type: 'ADD_ROW' as OperationType,
              timestamp: Date.now(),
              params: { insertAfter, level }
            }
          };
        });
      },

      // 删除行
      deleteRow: ({ rowId }) => {
        set((state) => {
          if (!state.rows[rowId]) return state;
          
          const newRows = { ...state.rows };
          delete newRows[rowId];
          
          const newRootIds = state.rootIds.filter(id => id !== rowId);
          const newSelectedIds = state.selectedIds.filter(id => id !== rowId);
          
          return {
            rows: newRows,
            rootIds: newRootIds,
            selectedIds: newSelectedIds,
            lastOperation: {
              type: 'DELETE_ROW' as OperationType,
              timestamp: Date.now(),
              params: { rowId }
            }
          };
        });
      },

      // 更新单元格
      updateCell: ({ rowId, field, value }) => {
        set((state) => {
          if (!state.rows[rowId]) return state;
          
          const updatedRow = {
            ...state.rows[rowId],
            [field]: value,
            updatedAt: Date.now()
          };
          
          return {
            rows: {
              ...state.rows,
              [rowId]: updatedRow
            },
            lastOperation: {
              type: 'UPDATE_CELL' as OperationType,
              timestamp: Date.now(),
              params: { rowId, field, value }
            }
          };
        });
      },

      // 层级上调
      levelUp: ({ rowId }) => {
        set((state) => {
          const row = state.rows[rowId];
          if (!row) return state;
          
          const currentLevelNum = CodingEngine.getSegmentCountByLevel(row.level);
          if (currentLevelNum >= 5) return state; // 已达到最高层级
          
          const newLevel = CodingEngine.getLevelBySegmentCount(currentLevelNum + 1);
          const allRows = Object.values(state.rows);
          const newCode = CodingEngine.generateLevelUpCode(row.code, allRows);
          
          const updatedRow = {
            ...row,
            level: newLevel,
            code: newCode,
            updatedAt: Date.now()
          };
          
          return {
            rows: {
              ...state.rows,
              [rowId]: updatedRow
            },
            lastOperation: {
              type: 'LEVEL_UP' as OperationType,
              timestamp: Date.now(),
              params: { rowId }
            }
          };
        });
      },

      // 层级下调
      levelDown: ({ rowId }) => {
        set((state) => {
          const row = state.rows[rowId];
          if (!row) return state;
          
          const currentLevelNum = CodingEngine.getSegmentCountByLevel(row.level);
          if (currentLevelNum <= 1) return state; // 已达到最低层级
          
          const newLevel = CodingEngine.getLevelBySegmentCount(currentLevelNum - 1);
          const allRows = Object.values(state.rows);
          const newCode = CodingEngine.generateLevelDownCode(row.code, allRows);
          
          const updatedRow = {
            ...row,
            level: newLevel,
            code: newCode,
            updatedAt: Date.now()
          };
          
          return {
            rows: {
              ...state.rows,
              [rowId]: updatedRow
            },
            lastOperation: {
              type: 'LEVEL_DOWN' as OperationType,
              timestamp: Date.now(),
              params: { rowId }
            }
          };
        });
      },

      // 添加动态列
      addColumn: ({ title = '新标题', afterColumn }) => {
        set((state) => {
          const newColumnNumber = state.dynamicColumns.length + 4; // title4, title5...
          const newColumn: DynamicColumn = {
            key: `title${newColumnNumber}`,
            title,
            width: 200,
            resizable: true,
            sortable: false,
            order: state.dynamicColumns.length,
            createdAt: Date.now()
          };
          
          return {
            dynamicColumns: [...state.dynamicColumns, newColumn],
            lastOperation: {
              type: 'ADD_COLUMN' as OperationType,
              timestamp: Date.now(),
              params: { title, afterColumn }
            }
          };
        });
      },

      // 删除动态列
      deleteColumn: ({ columnKey }) => {
        set((state) => ({
          dynamicColumns: state.dynamicColumns.filter(col => col.key !== columnKey),
          lastOperation: {
            type: 'DELETE_COLUMN' as OperationType,
            timestamp: Date.now(),
            params: { columnKey }
          }
        }));
      },

      // 选择行
      selectRow: (rowId) => {
        set((state) => ({
          selectedIds: [rowId]
        }));
      },

      // 多选
      selectMultiple: (rowIds) => {
        set(() => ({
          selectedIds: rowIds
        }));
      },

      // 清除选择
      clearSelection: () => {
        set(() => ({
          selectedIds: []
        }));
      },

      // 开始编辑
      startEdit: (rowId, field) => {
        set(() => ({
          editingCell: { rowId, field }
        }));
      },

      // 停止编辑
      stopEdit: () => {
        set(() => ({
          editingCell: null
        }));
      },

      // 获取字段启用状态
      getFieldEnabledState: (level, hasTitle4, hasTitle5) => {
        const levelNum = CodingEngine.getSegmentCountByLevel(level);
        return {
          title1: true,
          title2: levelNum >= 2,
          title3: levelNum >= 3,
          title4: hasTitle4 && levelNum >= 4,
          title5: hasTitle5 && levelNum >= 5,
          description: levelNum >= 4 // 最高层级可填写描述
        };
      },

      // 获取当前最高层级
      getMaxLevel: () => {
        const state = get();
        const hasTitle4 = state.dynamicColumns.some(col => col.key === 'title4');
        const hasTitle5 = state.dynamicColumns.some(col => col.key === 'title5');
        
        if (hasTitle5) return 'LV5';
        if (hasTitle4) return 'LV4';
        return 'LV3';
      },

      // 初始化示例数据
      initializeWithSampleData: () => {
        set(() => {
          const sampleRows: Record<string, TableRow> = {
            'row1': {
              id: 'row1',
              code: 'A001',
              level: 'LV1',
              title1: '项目管理系统',
              parentId: undefined,
              children: [],
              position: 1,
              isExpanded: true,
              createdAt: Date.now(),
              updatedAt: Date.now()
            }
          };
          
          return {
            rows: sampleRows,
            rootIds: ['row1'],
            selectedIds: [],
            editingCell: null,
            dynamicColumns: [],
            expandedIds: new Set(['row1']),
            lastOperation: null
          };
        });
      }
    }),
    {
      name: 'horizontal-table-store'
    }
  )
);
