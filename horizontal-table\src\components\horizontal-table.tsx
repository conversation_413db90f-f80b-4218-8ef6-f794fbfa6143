/**
 * 横向层级表格组件
 * 基于正确需求理解设计的表格组件
 * 支持横向展示的层级数据管理
 */

'use client';

import React, { useEffect } from 'react';
import { useTableStore } from '@/store/table-store';
import { HorizontalRow } from './horizontal-row';
import { TableToolbar } from './table-toolbar';

interface HorizontalTableProps {
  height?: number;
  className?: string;
}

export function HorizontalTable({ height = 600, className = '' }: HorizontalTableProps) {
  const {
    rows,
    rootIds,
    selectedIds,
    dynamicColumns,
    addRow,
    deleteRow,
    updateCell,
    levelUp,
    levelDown,
    addColumn,
    deleteColumn,
    selectRow,
    getFieldEnabledState,
    initializeWithSampleData
  } = useTableStore();

  // 初始化示例数据
  useEffect(() => {
    if (Object.keys(rows).length === 0) {
      initializeWithSampleData();
    }
  }, [rows, initializeWithSampleData]);

  // 处理添加行
  const handleAddRow = (afterRowId?: string) => {
    addRow({ insertAfter: afterRowId });
  };

  // 处理删除行
  const handleDeleteRow = (rowId: string) => {
    deleteRow({ rowId });
  };

  // 处理层级调整
  const handleLevelUp = (rowId: string) => {
    levelUp({ rowId });
  };

  const handleLevelDown = (rowId: string) => {
    levelDown({ rowId });
  };

  // 处理单元格更新
  const handleUpdateCell = (rowId: string, field: keyof typeof rows[string], value: string) => {
    updateCell({ rowId, field, value });
  };

  // 处理添加列
  const handleAddColumn = () => {
    const hasTitle4 = dynamicColumns.some(col => col.key === 'title4');
    const hasTitle5 = dynamicColumns.some(col => col.key === 'title5');
    
    if (!hasTitle4) {
      addColumn({ title: '标题4' });
    } else if (!hasTitle5) {
      addColumn({ title: '标题5' });
    }
  };

  // 渲染表头
  const renderTableHeader = () => {
    const hasTitle4 = dynamicColumns.some(col => col.key === 'title4');
    const hasTitle5 = dynamicColumns.some(col => col.key === 'title5');

    return (
      <div className="bg-gray-50 border-b border-gray-200 flex text-sm font-medium text-gray-700 sticky top-0 z-10">
        <div className="px-4 py-3 border-r border-gray-200 w-20 flex-shrink-0">操作</div>
        <div className="px-4 py-3 border-r border-gray-200 w-32 flex-shrink-0">编码</div>
        <div className="px-4 py-3 border-r border-gray-200 w-20 flex-shrink-0">层级</div>
        <div className="px-4 py-3 border-r border-gray-200 w-48 flex-shrink-0">标题1</div>
        <div className="px-4 py-3 border-r border-gray-200 w-48 flex-shrink-0">标题2</div>
        <div className="px-4 py-3 border-r border-gray-200 w-48 flex-shrink-0">标题3</div>
        {hasTitle4 && (
          <div className="px-4 py-3 border-r border-gray-200 w-48 flex-shrink-0">标题4</div>
        )}
        {hasTitle5 && (
          <div className="px-4 py-3 border-r border-gray-200 w-48 flex-shrink-0">标题5</div>
        )}
        <div className="px-4 py-3 flex-1">细节描述</div>
      </div>
    );
  };

  // 渲染行数据
  const renderRows = () => {
    return rootIds.map(rowId => {
      const row = rows[rowId];
      if (!row) return null;

      const hasTitle4 = dynamicColumns.some(col => col.key === 'title4');
      const hasTitle5 = dynamicColumns.some(col => col.key === 'title5');
      const fieldEnabled = getFieldEnabledState(row.level, hasTitle4, hasTitle5);

      return (
        <HorizontalRow
          key={row.id}
          row={row}
          fieldEnabled={fieldEnabled}
          isSelected={selectedIds.includes(row.id)}
          hasTitle4={hasTitle4}
          hasTitle5={hasTitle5}
          onAddRow={() => handleAddRow(row.id)}
          onDeleteRow={() => handleDeleteRow(row.id)}
          onLevelUp={() => handleLevelUp(row.id)}
          onLevelDown={() => handleLevelDown(row.id)}
          onUpdateCell={handleUpdateCell}
          onSelect={() => selectRow(row.id)}
        />
      );
    });
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm overflow-hidden ${className}`}>
      {/* 工具栏 */}
      <TableToolbar
        rowCount={Object.keys(rows).length}
        onAddRow={() => handleAddRow()}
        onAddColumn={handleAddColumn}
        canAddColumn={dynamicColumns.length < 2} // 最多支持标题4、标题5
      />

      {/* 表格容器 */}
      <div 
        className="overflow-auto"
        style={{ height: `${height}px` }}
      >
        {/* 表头 */}
        {renderTableHeader()}

        {/* 表格内容 */}
        <div className="divide-y divide-gray-200">
          {renderRows()}
        </div>

        {/* 空状态 */}
        {Object.keys(rows).length === 0 && (
          <div className="flex items-center justify-center h-32 text-gray-500">
            <div className="text-center">
              <p className="text-lg font-medium">暂无数据</p>
              <p className="text-sm">点击"添加行"开始创建您的层级表格</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// 默认导出
export default HorizontalTable;
