/**
 * 行操作组件
 * 提供添加、删除、层级调整等操作按钮
 */

'use client';

import React from 'react';
import { Level } from '@/types/table';

interface RowActionsProps {
  rowId: string;
  level: Level;
  isVisible: boolean;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onLevelUp: () => void;
  onLevelDown: () => void;
}

export function RowActions({
  rowId,
  level,
  isVisible,
  onAddRow,
  onDeleteRow,
  onLevelUp,
  onLevelDown
}: RowActionsProps) {
  // 检查是否可以上调层级
  const canLevelUp = level !== 'LV5';
  
  // 检查是否可以下调层级
  const canLevelDown = level !== 'LV1';

  const handleClick = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation(); // 阻止事件冒泡
    action();
  };

  return (
    <div 
      className={`
        flex gap-1 transition-opacity duration-200
        ${isVisible ? 'opacity-100' : 'opacity-0'}
      `}
      data-testid="row-actions"
    >
      {/* 添加行按钮 */}
      <button
        onClick={(e) => handleClick(e, onAddRow)}
        className="p-1 text-blue-600 hover:bg-blue-50 rounded text-xs transition-colors"
        title="添加行"
        data-testid="add-row-button"
      >
        ➕
      </button>

      {/* 删除行按钮 */}
      <button
        onClick={(e) => handleClick(e, onDeleteRow)}
        className="p-1 text-red-600 hover:bg-red-50 rounded text-xs transition-colors"
        title="删除行"
        data-testid="delete-row-button"
      >
        🗑️
      </button>

      {/* 层级上调按钮 */}
      <button
        onClick={(e) => handleClick(e, onLevelUp)}
        disabled={!canLevelUp}
        className={`
          p-1 rounded text-xs transition-colors
          ${canLevelUp 
            ? 'text-green-600 hover:bg-green-50' 
            : 'text-gray-400 cursor-not-allowed'
          }
        `}
        title={canLevelUp ? '层级上调' : '已达到最高层级'}
        data-testid="level-up-button"
      >
        ⬆️
      </button>

      {/* 层级下调按钮 */}
      <button
        onClick={(e) => handleClick(e, onLevelDown)}
        disabled={!canLevelDown}
        className={`
          p-1 rounded text-xs transition-colors
          ${canLevelDown 
            ? 'text-orange-600 hover:bg-orange-50' 
            : 'text-gray-400 cursor-not-allowed'
          }
        `}
        title={canLevelDown ? '层级下调' : '已达到最低层级'}
        data-testid="level-down-button"
      >
        ⬇️
      </button>
    </div>
  );
}

// 默认导出
export default RowActions;
