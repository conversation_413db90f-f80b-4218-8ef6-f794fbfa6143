/**
 * 横向表格行组件
 * 单个数据行的横向展示组件
 */

'use client';

import React, { useState } from 'react';
import { TableRow, FieldEnabledState } from '@/types/table';
import { RowActions } from './row-actions';
import { CodeDisplay } from './code-display';
import { LevelBadge } from './level-badge';
import { InlineEditor } from './inline-editor';

interface HorizontalRowProps {
  row: TableRow;
  fieldEnabled: FieldEnabledState;
  isSelected: boolean;
  hasTitle4: boolean;
  hasTitle5: boolean;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onLevelUp: () => void;
  onLevelDown: () => void;
  onUpdateCell: (rowId: string, field: keyof TableRow, value: string) => void;
  onSelect: () => void;
}

export function HorizontalRow({
  row,
  fieldEnabled,
  isSelected,
  hasTitle4,
  hasTitle5,
  onAddRow,
  onDeleteRow,
  onLevelUp,
  onLevelDown,
  onUpdateCell,
  onSelect
}: HorizontalRowProps) {
  const [isHovered, setIsHovered] = useState(false);

  // 处理字段更新
  const handleFieldUpdate = (field: keyof TableRow, value: string) => {
    onUpdateCell(row.id, field, value);
  };

  // 渲染字段内容
  const renderField = (
    field: keyof TableRow,
    value: string | undefined,
    enabled: boolean,
    placeholder: string
  ) => {
    if (!enabled) {
      return (
        <div className="px-2 py-1 text-gray-400 text-sm bg-gray-50 rounded">
          /
        </div>
      );
    }

    return (
      <InlineEditor
        value={value || ''}
        placeholder={placeholder}
        onChange={(newValue) => handleFieldUpdate(field, newValue)}
        className="w-full"
      />
    );
  };

  return (
    <div
      className={`
        flex border-b border-gray-200 hover:bg-gray-50 group transition-colors cursor-pointer
        ${isSelected ? 'bg-blue-50 border-blue-200' : ''}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onSelect}
      data-testid="horizontal-row"
    >
      {/* 操作列 */}
      <div className="px-4 py-3 w-20 border-r border-gray-200 flex-shrink-0">
        <RowActions
          rowId={row.id}
          level={row.level}
          isVisible={isHovered}
          onAddRow={onAddRow}
          onDeleteRow={onDeleteRow}
          onLevelUp={onLevelUp}
          onLevelDown={onLevelDown}
        />
      </div>

      {/* 编码列 */}
      <div className="px-4 py-3 w-32 border-r border-gray-200 flex-shrink-0">
        <CodeDisplay 
          code={row.code}
          level={row.level}
        />
      </div>

      {/* 层级列 */}
      <div className="px-4 py-3 w-20 border-r border-gray-200 flex-shrink-0">
        <LevelBadge level={row.level} />
      </div>

      {/* 标题1列 */}
      <div className="px-4 py-3 w-48 border-r border-gray-200 flex-shrink-0">
        {renderField('title1', row.title1, fieldEnabled.title1, '标题1')}
      </div>

      {/* 标题2列 */}
      <div className="px-4 py-3 w-48 border-r border-gray-200 flex-shrink-0">
        {renderField('title2', row.title2, fieldEnabled.title2, '标题2')}
      </div>

      {/* 标题3列 */}
      <div className="px-4 py-3 w-48 border-r border-gray-200 flex-shrink-0">
        {renderField('title3', row.title3, fieldEnabled.title3, '标题3')}
      </div>

      {/* 标题4列（动态） */}
      {hasTitle4 && (
        <div className="px-4 py-3 w-48 border-r border-gray-200 flex-shrink-0">
          {renderField('title4', row.title4, fieldEnabled.title4, '标题4')}
        </div>
      )}

      {/* 标题5列（动态） */}
      {hasTitle5 && (
        <div className="px-4 py-3 w-48 border-r border-gray-200 flex-shrink-0">
          {renderField('title5', row.title5, fieldEnabled.title5, '标题5')}
        </div>
      )}

      {/* 细节描述列 */}
      <div className="px-4 py-3 flex-1">
        {renderField('description', row.description, fieldEnabled.description, '细节描述')}
      </div>
    </div>
  );
}

// 默认导出
export default HorizontalRow;
