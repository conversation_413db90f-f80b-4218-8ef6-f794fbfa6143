/**
 * 模块2：编码引擎 - 简化版
 * 基于需求文档实现A001系列编码算法
 */

export interface TableRow {
  id: string;
  code: string;
  level: string;
  title1: string;
  title2: string;
  title3: string;
  description: string;
}

export class CodingEngine {
  /**
   * 解析编码为段数组
   * A001 -> [1]
   * A002001 -> [2, 1] 
   * A002002001 -> [2, 2, 1]
   */
  static parseCode(code: string): number[] {
    if (!code.startsWith('A')) {
      throw new Error('Invalid code format');
    }
    
    const numberPart = code.substring(1);
    const segments: number[] = [];
    
    for (let i = 0; i < numberPart.length; i += 3) {
      const segment = numberPart.substring(i, i + 3);
      segments.push(parseInt(segment, 10));
    }
    
    return segments;
  }

  /**
   * 构建编码
   * [1] -> A001
   * [2, 1] -> A002001
   * [2, 2, 1] -> A002002001
   */
  static buildCode(segments: number[]): string {
    const numberPart = segments
      .map(segment => String(segment).padStart(3, '0'))
      .join('');
    return `A${numberPart}`;
  }

  /**
   * 获取层级
   * 1段 -> LV1, 2段 -> LV2, 3段 -> LV3, 4段 -> LV4
   */
  static getLevel(segments: number[]): string {
    return `LV${segments.length}`;
  }

  /**
   * 添加行 - 根据需求文档规则
   * 新增行比当前行少一个单元格内容，编码少一节，最后一节编码+1
   */
  static generateAddRowCode(currentCode: string, allRows: TableRow[]): { code: string; level: string } {
    const currentSegments = this.parseCode(currentCode);
    
    // 新行编码段数 = 当前行编码段数 - 1
    const newSegmentCount = Math.max(1, currentSegments.length - 1);
    
    if (newSegmentCount === 1) {
      // 生成LV1编码：找到最大的LV1编码+1
      const lv1Codes = allRows
        .filter(row => this.parseCode(row.code).length === 1)
        .map(row => this.parseCode(row.code)[0])
        .sort((a, b) => b - a);
      
      const nextNumber = lv1Codes.length > 0 ? lv1Codes[0] + 1 : 1;
      return {
        code: this.buildCode([nextNumber]),
        level: 'LV1'
      };
    } else {
      // 生成多段编码：取当前编码的前n-1段，最后一段+1
      const parentSegments = currentSegments.slice(0, newSegmentCount - 1);
      const lastSegment = currentSegments[newSegmentCount - 1] + 1;
      
      const newSegments = [...parentSegments, lastSegment];
      return {
        code: this.buildCode(newSegments),
        level: this.getLevel(newSegments)
      };
    }
  }

  /**
   * 删除行后重新排序
   * 根据需求文档：下方行按删除行上一行编码排序重新生成
   */
  static reorderAfterDelete(deletedRowIndex: number, allRows: TableRow[]): TableRow[] {
    const newRows = [...allRows];
    newRows.splice(deletedRowIndex, 1);
    
    // 简化实现：重新生成所有编码
    return this.regenerateAllCodes(newRows);
  }

  /**
   * 层级上调
   * 根据需求文档：上调一个层级，继承层级内上一行内容，编号多一节
   */
  static levelUp(rowIndex: number, allRows: TableRow[]): TableRow[] {
    const newRows = [...allRows];
    const currentRow = newRows[rowIndex];
    const currentSegments = this.parseCode(currentRow.code);
    
    // 不能超过4个段（LV4是最高级）
    if (currentSegments.length >= 4) {
      return newRows;
    }
    
    // 继承上一行内容
    const prevRow = rowIndex > 0 ? newRows[rowIndex - 1] : null;
    if (prevRow) {
      const newSegments = [...currentSegments, 1];
      newRows[rowIndex] = {
        ...currentRow,
        code: this.buildCode(newSegments),
        level: this.getLevel(newSegments),
        title1: prevRow.title1,
        title2: prevRow.title2 || currentRow.title2,
        title3: prevRow.title3 || currentRow.title3
      };
    }
    
    return newRows;
  }

  /**
   * 层级下调
   * 根据需求文档：下调一个层级，少一个单元格，编号少一节
   */
  static levelDown(rowIndex: number, allRows: TableRow[]): TableRow[] {
    const newRows = [...allRows];
    const currentRow = newRows[rowIndex];
    const currentSegments = this.parseCode(currentRow.code);
    
    // 不能少于1个段（LV1是最低级）
    if (currentSegments.length <= 1) {
      return newRows;
    }
    
    // 减少一节
    const newSegments = currentSegments.slice(0, -1);
    newRows[rowIndex] = {
      ...currentRow,
      code: this.buildCode(newSegments),
      level: this.getLevel(newSegments),
      // 根据层级清空对应字段
      title3: newSegments.length < 3 ? '' : currentRow.title3,
      title2: newSegments.length < 2 ? '' : currentRow.title2,
      description: newSegments.length < 4 ? '' : currentRow.description
    };
    
    return newRows;
  }

  /**
   * 重新生成所有编码 - 简化实现
   */
  private static regenerateAllCodes(rows: TableRow[]): TableRow[] {
    // 简化实现：按顺序重新编号
    return rows.map((row, index) => {
      const segments = this.parseCode(row.code);
      // 保持层级结构，重新编号
      return {
        ...row,
        code: this.buildCode([index + 1, ...segments.slice(1)])
      };
    });
  }

  /**
   * 验证编码格式
   */
  static validateCode(code: string): boolean {
    try {
      const segments = this.parseCode(code);
      return segments.length > 0 && segments.length <= 4 && segments.every(s => s > 0);
    } catch {
      return false;
    }
  }

  /**
   * 获取字段启用状态
   */
  static getFieldEnabled(level: string) {
    const levelNum = parseInt(level.substring(2));
    return {
      title1: true,
      title2: levelNum >= 2,
      title3: levelNum >= 3,
      description: levelNum >= 4
    };
  }

  /**
   * 模块3：智能内容继承
   * 层级上调时，智能继承上一行对应层级的内容
   */
  static inheritContentSmart(currentRow: TableRow, targetLevel: string, allRows: TableRow[], currentIndex: number): Partial<TableRow> {
    const targetLevelNum = parseInt(targetLevel.substring(2));
    const result: Partial<TableRow> = {};

    // 寻找上一行作为继承源
    const prevRow = currentIndex > 0 ? allRows[currentIndex - 1] : null;

    if (prevRow) {
      // 继承对应层级的内容
      result.title1 = prevRow.title1 || currentRow.title1;

      if (targetLevelNum >= 2) {
        result.title2 = prevRow.title2 || currentRow.title2 || '';
      }

      if (targetLevelNum >= 3) {
        result.title3 = prevRow.title3 || currentRow.title3 || '';
      }

      if (targetLevelNum >= 4) {
        result.description = prevRow.description || currentRow.description || '';
      }
    }

    return result;
  }

  /**
   * 模块3：同层级内容同步
   * 当某行内容更新时，同步到同层级的其他行
   */
  static syncSameLevelContent(updatedRow: TableRow, allRows: TableRow[]): TableRow[] {
    const updatedSegments = this.parseCode(updatedRow.code);
    const updatedLevel = this.getLevel(updatedSegments);

    return allRows.map(row => {
      const rowSegments = this.parseCode(row.code);
      const rowLevel = this.getLevel(rowSegments);

      // 如果是同层级且不是当前更新的行
      if (rowLevel === updatedLevel && row.id !== updatedRow.id) {
        // 同步对应层级的内容
        const syncedRow = { ...row };

        // 根据层级同步内容
        if (updatedLevel === 'LV1') {
          syncedRow.title1 = updatedRow.title1;
        } else if (updatedLevel === 'LV2') {
          syncedRow.title1 = updatedRow.title1;
          syncedRow.title2 = updatedRow.title2;
        } else if (updatedLevel === 'LV3') {
          syncedRow.title1 = updatedRow.title1;
          syncedRow.title2 = updatedRow.title2;
          syncedRow.title3 = updatedRow.title3;
        } else if (updatedLevel === 'LV4') {
          syncedRow.title1 = updatedRow.title1;
          syncedRow.title2 = updatedRow.title2;
          syncedRow.title3 = updatedRow.title3;
          syncedRow.description = updatedRow.description;
        }

        return syncedRow;
      }

      return row;
    });
  }

  /**
   * 模块3：层级操作验证
   * 验证层级上调/下调是否允许
   */
  static validateLevelOperation(operation: 'up' | 'down', currentLevel: string): { allowed: boolean; reason?: string } {
    const currentLevelNum = parseInt(currentLevel.substring(2));

    if (operation === 'up') {
      if (currentLevelNum >= 4) {
        return { allowed: false, reason: '已达到最高层级LV4' };
      }
      return { allowed: true };
    } else {
      if (currentLevelNum <= 1) {
        return { allowed: false, reason: '已达到最低层级LV1' };
      }
      return { allowed: true };
    }
  }

  /**
   * 模块3：增强版层级上调
   * 集成智能内容继承和验证
   */
  static levelUpEnhanced(rowIndex: number, allRows: TableRow[]): { success: boolean; rows?: TableRow[]; error?: string } {
    const currentRow = allRows[rowIndex];
    const validation = this.validateLevelOperation('up', currentRow.level);

    if (!validation.allowed) {
      return { success: false, error: validation.reason };
    }

    const currentSegments = this.parseCode(currentRow.code);
    const newSegments = [...currentSegments, 1];
    const newLevel = this.getLevel(newSegments);

    // 智能内容继承
    const inheritedContent = this.inheritContentSmart(currentRow, newLevel, allRows, rowIndex);

    const newRows = [...allRows];
    newRows[rowIndex] = {
      ...currentRow,
      code: this.buildCode(newSegments),
      level: newLevel,
      ...inheritedContent
    };

    return { success: true, rows: newRows };
  }

  /**
   * 模块3：增强版层级下调
   * 集成字段清理和验证
   */
  static levelDownEnhanced(rowIndex: number, allRows: TableRow[]): { success: boolean; rows?: TableRow[]; error?: string } {
    const currentRow = allRows[rowIndex];
    const validation = this.validateLevelOperation('down', currentRow.level);

    if (!validation.allowed) {
      return { success: false, error: validation.reason };
    }

    const currentSegments = this.parseCode(currentRow.code);
    const newSegments = currentSegments.slice(0, -1);
    const newLevel = this.getLevel(newSegments);

    const newRows = [...allRows];
    newRows[rowIndex] = {
      ...currentRow,
      code: this.buildCode(newSegments),
      level: newLevel,
      // 根据新层级清理字段
      title3: newSegments.length < 3 ? '' : currentRow.title3,
      title2: newSegments.length < 2 ? '' : currentRow.title2,
      description: newSegments.length < 4 ? '' : currentRow.description
    };

    return { success: true, rows: newRows };
  }

  /**
   * 模块4：智能行插入
   * 在指定位置插入新行，自动生成合适的编码
   */
  static insertRowAt(position: number, allRows: TableRow[], template?: Partial<TableRow>): { success: boolean; rows?: TableRow[]; newRow?: TableRow; error?: string } {
    try {
      const newRows = [...allRows];

      // 确定新行的编码和层级
      let newCode: string;
      let newLevel: string;

      if (position === 0) {
        // 插入到开头，生成第一个LV1编码
        newCode = 'A001';
        newLevel = 'LV1';
      } else if (position >= allRows.length) {
        // 插入到末尾，基于最后一行生成
        const lastRow = allRows[allRows.length - 1];
        const result = this.generateAddRowCode(lastRow.code, allRows);
        newCode = result.code;
        newLevel = result.level;
      } else {
        // 插入到中间，基于前一行生成
        const prevRow = allRows[position - 1];
        const result = this.generateAddRowCode(prevRow.code, allRows);
        newCode = result.code;
        newLevel = result.level;
      }

      // 创建新行
      const newRow: TableRow = {
        id: `row_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        code: newCode,
        level: newLevel,
        title1: template?.title1 || `新项目 ${newCode}`,
        title2: template?.title2 || '',
        title3: template?.title3 || '',
        description: template?.description || ''
      };

      // 插入新行
      newRows.splice(position, 0, newRow);

      // 重新排序后续行的编码
      const reorderedRows = this.reorderRowsAfterInsert(newRows, position);

      return { success: true, rows: reorderedRows, newRow };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * 模块4：批量删除行
   * 删除指定的多行，重新排序编码
   */
  static deleteRows(rowIds: string[], allRows: TableRow[]): { success: boolean; rows?: TableRow[]; deletedCount?: number; error?: string } {
    try {
      if (rowIds.length === 0) {
        return { success: false, error: '没有选择要删除的行' };
      }

      // 过滤掉要删除的行
      const remainingRows = allRows.filter(row => !rowIds.includes(row.id));

      // 重新生成编码
      const reorderedRows = this.regenerateAllCodes(remainingRows);

      return {
        success: true,
        rows: reorderedRows,
        deletedCount: rowIds.length
      };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * 模块4：移动行
   * 将指定行移动到新位置，重新排序编码
   */
  static moveRows(rowIds: string[], targetPosition: number, allRows: TableRow[]): { success: boolean; rows?: TableRow[]; error?: string } {
    try {
      if (rowIds.length === 0) {
        return { success: false, error: '没有选择要移动的行' };
      }

      // 获取要移动的行
      const rowsToMove = allRows.filter(row => rowIds.includes(row.id));
      const remainingRows = allRows.filter(row => !rowIds.includes(row.id));

      // 在目标位置插入移动的行
      const newRows = [...remainingRows];
      newRows.splice(targetPosition, 0, ...rowsToMove);

      // 重新生成编码
      const reorderedRows = this.regenerateAllCodes(newRows);

      return { success: true, rows: reorderedRows };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * 模块4：复制行
   * 复制指定行到新位置，生成新编码
   */
  static copyRows(rowIds: string[], targetPosition: number, allRows: TableRow[]): { success: boolean; rows?: TableRow[]; newRows?: TableRow[]; error?: string } {
    try {
      if (rowIds.length === 0) {
        return { success: false, error: '没有选择要复制的行' };
      }

      // 获取要复制的行
      const rowsToCopy = allRows.filter(row => rowIds.includes(row.id));

      // 创建复制的行（新ID和编码）
      const copiedRows = rowsToCopy.map(row => ({
        ...row,
        id: `row_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        code: `${row.code}_copy` // 临时编码，后续会重新生成
      }));

      // 在目标位置插入复制的行
      const newRows = [...allRows];
      newRows.splice(targetPosition, 0, ...copiedRows);

      // 重新生成所有编码
      const reorderedRows = this.regenerateAllCodes(newRows);

      return { success: true, rows: reorderedRows, newRows: copiedRows };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * 模块4：插入后重新排序
   */
  private static reorderRowsAfterInsert(rows: TableRow[], insertPosition: number): TableRow[] {
    // 简化实现：重新生成所有编码
    return this.regenerateAllCodes(rows);
  }

  /**
   * 模块4：重新生成所有行的编码 - 修复版
   * 根据行的层级结构重新分配编码
   */
  static regenerateAllCodes(rows: TableRow[]): TableRow[] {
    if (rows.length === 0) return [];

    const result: TableRow[] = [];
    let globalCounter = 1;

    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      const level = row.level;
      const levelNum = parseInt(level.substring(2));

      // 简化编码生成：使用全局计数器
      let newCode = 'A';

      // 根据层级生成编码段
      if (levelNum === 1) {
        newCode += globalCounter.toString().padStart(3, '0');
      } else if (levelNum === 2) {
        newCode += globalCounter.toString().padStart(3, '0') + '001';
      } else if (levelNum === 3) {
        newCode += globalCounter.toString().padStart(3, '0') + '002001';
      } else if (levelNum === 4) {
        newCode += globalCounter.toString().padStart(3, '0') + '002002001';
      }

      // 创建新行
      result.push({
        ...row,
        code: newCode
      });

      globalCounter++;
    }

    return result;
  }

  /**
   * 模块4：优化复制行功能
   * 修复编码生成问题
   */
  static copyRowsOptimized(rowIds: string[], targetPosition: number, allRows: TableRow[]): { success: boolean; rows?: TableRow[]; newRows?: TableRow[]; error?: string } {
    try {
      if (rowIds.length === 0) {
        return { success: false, error: '没有选择要复制的行' };
      }

      // 获取要复制的行
      const rowsToCopy = allRows.filter(row => rowIds.includes(row.id));

      // 创建复制的行（新ID，保持原有层级）
      const copiedRows = rowsToCopy.map(row => ({
        ...row,
        id: `row_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        title1: `${row.title1}_副本` || `新项目_副本`,
        // 保持原有层级，编码将在重新生成时分配
        code: row.code // 临时保持，后续会重新生成
      }));

      // 在目标位置插入复制的行
      const newRows = [...allRows];
      newRows.splice(targetPosition, 0, ...copiedRows);

      // 重新生成所有编码
      const reorderedRows = this.regenerateAllCodes(newRows);

      return { success: true, rows: reorderedRows, newRows: copiedRows };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
}

export default CodingEngine;
