# 技术架构设计 - Notion风格层级表格管理系统

## 1. 架构文档信息

| 项目 | 信息 |
|------|------|
| 系统名称 | Notion风格层级表格管理系统 |
| 架构版本 | v1.0 |
| 创建日期 | 2025-01-20 |
| 架构师 | Bob |
| 状态 | 设计完成 |

## 2. 技术选型评估

### 2.1 前端框架选型

#### ✅ Next.js 14 + React 18 + TypeScript
**选择理由**：
- **性能优势**：App Router + Server Components，优化首屏加载
- **开发体验**：热重载、TypeScript原生支持、优秀的开发工具
- **生态完善**：丰富的组件库和工具链支持
- **SEO友好**：SSR/SSG支持（虽然本项目主要是SPA）

### 2.2 状态管理选型

#### ✅ Zustand (推荐方案)
**选择理由**：
- **轻量级**：包体积小，性能优秀
- **简单易用**：API简洁，学习成本低
- **TypeScript友好**：原生TypeScript支持
- **适合场景**：中等复杂度的状态管理需求

**vs Redux Toolkit**：
- Redux Toolkit更适合大型应用，本项目复杂度适中
- Zustand的简洁性更符合快速开发需求
- 性能差异不大，但Zustand代码量更少

### 2.3 拖拽库选型

#### ✅ @dnd-kit (推荐方案)
**选择理由**：
- **现代化设计**：基于React Hooks，API设计优秀
- **无障碍支持**：内置键盘导航和屏幕阅读器支持
- **性能优秀**：虚拟化支持，适合大数据量
- **灵活性强**：高度可定制，支持复杂拖拽场景

**vs react-beautiful-dnd**：
- @dnd-kit更现代，维护更活跃
- 更好的TypeScript支持
- 更适合复杂的层级拖拽需求

### 2.4 样式方案选型

#### ✅ Tailwind CSS + CSS Modules
**选择理由**：
- **开发效率**：原子化CSS，快速构建UI
- **一致性**：设计系统内置，保证视觉一致性
- **性能**：按需加载，生产环境体积小
- **Notion风格**：易于实现现代化的设计效果

## 3. 系统架构设计

### 3.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Table     │  │   Toolbar   │  │  Sidebar    │         │
│  │ Components  │  │ Components  │  │ Components  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    Business Logic Layer                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Coding    │  │  Hierarchy  │  │   Drag &    │         │
│  │  Algorithm  │  │  Manager    │  │    Drop     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    Data Management Layer                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Zustand   │  │   Local     │  │  Validation │         │
│  │    Store    │  │   Storage   │  │   Engine    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    Infrastructure Layer                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Next.js   │  │   Virtual   │  │   Browser   │         │
│  │   Runtime   │  │  Scrolling  │  │     APIs    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 核心模块设计

#### 3.2.1 数据模型设计

```typescript
// 核心数据类型定义
interface TableRow {
  id: string;                    // 唯一标识
  code: string;                  // 编码 (A001, A002001等)
  level: Level;                  // 层级 (LV1-LV5)
  title1?: string;               // 标题1
  title2?: string;               // 标题2
  title3?: string;               // 标题3
  title4?: string;               // 标题4 (动态扩展)
  description?: string;          // 细节描述
  parentId?: string;             // 父级ID
  children: string[];            // 子级ID数组
  isExpanded: boolean;           // 展开状态
  order: number;                 // 同级排序
}

type Level = 'LV1' | 'LV2' | 'LV3' | 'LV4' | 'LV5';

interface TableState {
  rows: Record<string, TableRow>; // 行数据映射
  rootIds: string[];             // 根级行ID
  selectedIds: string[];         // 选中行ID
  editingCell: CellPosition | null; // 编辑状态
  dragState: DragState | null;   // 拖拽状态
}
```

#### 3.2.2 编码算法设计

```typescript
class CodingEngine {
  // 生成新编码
  generateCode(parentCode: string, level: Level, position: number): string {
    const segments = parentCode ? parentCode.split(/(\d+)/).filter(Boolean) : ['A'];
    const newSegment = String(position).padStart(3, '0');
    
    if (level === 'LV1') {
      return `A${newSegment}`;
    }
    
    return `${parentCode}${newSegment}`;
  }
  
  // 重新计算编码序列
  recalculateCodes(rows: TableRow[], parentId?: string): TableRow[] {
    // 复杂的编码重排算法实现
    // 处理添加、删除、移动后的编码更新
  }
  
  // 验证编码规则
  validateCode(code: string, level: Level): boolean {
    // 编码格式验证逻辑
  }
}
```

#### 3.2.3 层级管理器设计

```typescript
class HierarchyManager {
  // 层级上调
  promoteLevel(rowId: string, targetLevel: Level): TableRow[] {
    // 实现层级上调逻辑
    // 内容继承、编码调整、位置重排
  }
  
  // 层级下调
  demoteLevel(rowId: string, targetLevel: Level): TableRow[] {
    // 实现层级下调逻辑
    // 内容精简、编码调整、位置重排
  }
  
  // 内容同步
  syncContent(rowId: string, field: string, value: string): TableRow[] {
    // 同编码前缀的内容同步逻辑
  }
}
```

### 3.3 性能优化架构

#### 3.3.1 虚拟滚动方案

```typescript
// 使用 @tanstack/react-virtual
interface VirtualScrollConfig {
  itemHeight: number;           // 行高
  overscan: number;            // 预渲染行数
  scrollMargin: number;        // 滚动边距
}

class VirtualTableRenderer {
  // 计算可见行范围
  calculateVisibleRange(scrollTop: number, containerHeight: number): [number, number];
  
  // 渲染优化
  shouldUpdateRow(prevRow: TableRow, nextRow: TableRow): boolean;
}
```

#### 3.3.2 状态更新优化

```typescript
// Zustand store 设计
interface TableStore {
  // 状态
  state: TableState;
  
  // 操作方法
  actions: {
    addRow: (parentId?: string) => void;
    deleteRow: (rowId: string) => void;
    moveRow: (rowId: string, targetIndex: number) => void;
    updateCell: (rowId: string, field: string, value: string) => void;
    adjustLevel: (rowId: string, direction: 'up' | 'down') => void;
  };
  
  // 计算属性
  computed: {
    visibleRows: TableRow[];
    flattenedRows: TableRow[];
    selectedRows: TableRow[];
  };
}
```

## 4. 核心算法设计

### 4.1 编码生成算法

```typescript
/**
 * 编码生成核心算法
 * 规则：A + 3位数字段组合
 * 示例：A001, A001001, A001001001
 */
function generateHierarchicalCode(
  parentCode: string | null,
  level: Level,
  siblingIndex: number
): string {
  const baseCode = 'A';
  const segment = (siblingIndex + 1).toString().padStart(3, '0');
  
  if (!parentCode) {
    return `${baseCode}${segment}`;
  }
  
  return `${parentCode}${segment}`;
}
```

### 4.2 层级调整算法

```typescript
/**
 * 层级调整核心算法
 * 处理上调/下调时的内容继承和编码重排
 */
function adjustRowLevel(
  row: TableRow,
  targetLevel: Level,
  allRows: TableRow[]
): TableRow[] {
  // 1. 验证调整合法性
  // 2. 计算新的父级关系
  // 3. 重新生成编码
  // 4. 更新内容继承
  // 5. 重排序列
}
```

### 4.3 拖拽算法

```typescript
/**
 * 拖拽位置计算算法
 * 支持同级移动和跨级移动
 */
function calculateDropPosition(
  draggedRow: TableRow,
  targetRow: TableRow,
  dropPosition: 'before' | 'after' | 'inside'
): {
  newParentId: string | null;
  newIndex: number;
  levelChange: boolean;
} {
  // 复杂的位置计算逻辑
}
```

## 5. 组件架构设计

### 5.1 组件层次结构

```
TableContainer
├── TableToolbar
│   ├── AddRowButton
│   ├── ImportExportButtons
│   └── ViewOptionsMenu
├── TableHeader
│   └── ColumnHeaders[]
├── VirtualTableBody
│   └── TableRow[]
│       ├── RowHandle (拖拽手柄)
│       ├── RowActions (操作按钮)
│       ├── LevelIndicator (层级指示器)
│       └── TableCell[]
│           ├── EditableCell
│           └── ReadOnlyCell
└── TableFooter
    └── StatusBar
```

### 5.2 关键组件设计

#### TableRow 组件
```typescript
interface TableRowProps {
  row: TableRow;
  level: number;
  isSelected: boolean;
  isEditing: boolean;
  onEdit: (field: string, value: string) => void;
  onLevelChange: (direction: 'up' | 'down') => void;
  onDelete: () => void;
}
```

#### EditableCell 组件
```typescript
interface EditableCellProps {
  value: string;
  field: string;
  rowId: string;
  isEditing: boolean;
  onStartEdit: () => void;
  onSave: (value: string) => void;
  onCancel: () => void;
}
```

## 6. 数据流设计

### 6.1 状态管理流程

```
User Action → Component Event → Store Action → State Update → UI Re-render
     ↓              ↓              ↓              ↓              ↓
  点击编辑    →   onEdit事件   →  updateCell   →   状态更新    →   重新渲染
  拖拽移动    →   onDrop事件   →   moveRow     →   编码重算    →   动画更新
  层级调整    →   onLevel事件  →  adjustLevel  →   内容同步    →   视图刷新
```

### 6.2 性能优化策略

1. **React.memo**：组件级别的渲染优化
2. **useMemo/useCallback**：计算和函数缓存
3. **虚拟滚动**：大数据量渲染优化
4. **防抖节流**：用户输入优化
5. **批量更新**：状态更新合并

## 7. 安全与错误处理

### 7.1 数据验证
- 编码格式验证
- 层级关系验证
- 内容长度限制
- 特殊字符过滤

### 7.2 错误边界
- 组件级错误捕获
- 状态恢复机制
- 用户友好的错误提示
- 操作撤销功能

## 8. 部署架构

### 8.1 构建优化
- Tree Shaking：移除未使用代码
- Code Splitting：按需加载
- 资源压缩：CSS/JS压缩
- 缓存策略：静态资源缓存

### 8.2 运行时优化
- Service Worker：离线支持
- 预加载策略：关键资源预加载
- 监控埋点：性能监控
- 错误上报：异常追踪

---

**架构设计状态**：✅ 完成  
**下一步**：开始项目实施和开发环境搭建
