一、表格字段及其功能
操作	编码	层级	标题1	标题2	标题3	细节描述
1、操作：
(1)添加：在该行下方新增一行
操作	编码	层级	标题1	标题2	标题3	细节描述
						
注意：新增行比该行少一个单元格内容，编码少一节，最后一节编码+1，新增行下方行按排序重新生成
例如1：
☆为当前行,    为新增行
原：
操作	编码	层级	标题1	标题2	标题3	细节描述
-	-	-	-	-	-	-
☆	A001001	LV2	项目管理系统1	移动端		/
	A002	LV1	项目管理系统2			
	A002001	LV2	项目管理系统2	Mac端		/
	A003001001001	LV4	项目管理系统3	PC端	首页	个人简介
新增行：
操作	编码	层级	标题1	标题2	标题3	细节描述
-	-	-	-	-	-	-
☆	A001001	LV2	项目管理系统1	移动端		/
	A002	LV1	项目管理系统2		/	/
	A003	LV1	项目管理系统3		/	/
	A003001	LV2	项目管理系统3	Mac端		/
	A004001001001	LV4	项目管理系统4	PC端	首页	个人简介
说明：新增行少标题2，编码从2节变成1节，最后一节001变成002，下方行编号由002变003,003变成004

例如2：
操作	编码	层级	标题1	标题2	标题3	细节描述
-	-	-	-	-	-	-
☆	A001002002002	LV4	项目管理系统1	移动端	首页	个人简介
	A001002003	LV3	项目管理系统1	移动端	首页	
说明：新增行少细节描述，编码从4节变成3节，最后一节002变成003
(2)删除：删除该行,下方行按删除行上一行编码排序重新生成
例如1：
☆为当前需删除行   
操作	编码	层级	标题1	标题2	标题3	细节描述
-	-	-	-	-	-	-
	A001001	LV2	项目管理系统1	移动端		/
☆	A002	LV1	项目管理系统2		/	/
	A003	LV1	项目管理系统3		/	/
	A003001	LV2	项目管理系统3	Mac端		/
	A004001001001	LV4	项目管理系统4	PC端	首页	个人简介
删除行后：
操作	编码	层级	标题1	标题2	标题3	细节描述
-	-	-	-	-	-	-
	A001001	LV2	项目管理系统1	移动端		/
	A002	LV1	项目管理系统2		/	/
	A002001	LV2	项目管理系统2	Mac端		/
	A003001001001	LV4	项目管理系统3	PC端	首页	个人简介
说明：原003变成002，原004变成003

例如2：
☆为当前需删除行   
操作	编码	层级	标题1	标题2	标题3	细节描述
-	-	-	-	-	-	-
	A001001	LV2	项目管理系统1	移动端		/
☆	A002001001001	LV1	项目管理系统2	Web端	首页	个人简介
	A002001001002	LV1	项目管理系统2	Web端	首页	消息通知
	A003001	LV2	项目管理系统3	Mac端		/
	A004001001001	LV4	项目管理系统4	PC端	首页	个人简介
删除行后：
操作	编码	层级	标题1	标题2	标题3	细节描述
-	-	-	-	-	-	-
	A001001	LV2	项目管理系统1	移动端		/
	A002001001001	LV1	项目管理系统2	Web端	首页	消息通知
	A003001	LV2	项目管理系统3	Mac端		/
	A004001001001	LV4	项目管理系统4	PC端	首页	个人简介
说明：原002变成001




例如2：
☆为当前行
操作	编码	层级	标题1	标题2	标题3	细节描述
-	-	-	-	-	-	-
☆	A001002002002	LV4	项目管理系统1	移动端	首页	个人简介
	A001002003	LV3	项目管理系统1	移动端	首页	
说明：新增行少细节描述，编码从4节变成3节，最后一节002变成003




(3)移动：同层级可移动位置，最后一节编号跟随位置调整
☆为当前行位置
操作	编码	层级	标题1	标题2	标题3	细节描述
-	-	-	-	-	-	-
☆	A001002002001	LV4	项目管理系统1	移动端	首页	个人简介
	A001002002002	LV4	项目管理系统1	移动端	首页	消息通知
	A001002002003	LV4	项目管理系统1	移动端	首页	代办事项
调整位置后1：
操作	编码	层级	标题1	标题2	标题3	细节描述
-	-	-	-	-	-	-
	A001002002001	LV4	项目管理系统1	移动端	首页	消息通知
☆	A001002002002	LV4	项目管理系统1	移动端	首页	个人简介
	A001002002003	LV4	项目管理系统1	移动端	首页	代办事项
说明：当前行从同层级第一行调整到第二行，001变成002；原002变成001
调整位置后2：
操作	编码	层级	标题1	标题2	标题3	细节描述
-	-	-	-	-	-	-
	A001002002001	LV4	项目管理系统1	移动端	首页	消息通知
	A001002002002	LV4	项目管理系统1	移动端	首页	代办事项
☆	A001002002003	LV4	项目管理系统1	移动端	首页	个人简介
说明：当前行从同层级第一行调整到第三行，001变成003；原002变成001；原003变成002
2、编码：
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001	LV1	项目管理系统1		/	/
	A002001	LV2	项目管理系统2	移动端		/
	A002002001	LV3	项目管理系统2	Web端	首页	
	A002002002001	LV4	项目管理系统2	Web端	模块	新建流程
	A002002002002	LV4	项目管理系统2	Web端	模块	流程台账
	A003	LV1	项目管理系统3		/	/
A：工程文件，流程图第一个节点（起点）
001：一个标题或细节描述
A001:当前行写了标题1
A001001:当前行写了标题1、标题2
表格行顺序严格按照从小到大，从001起始，相同层级编号+1，不同层级按最少层级+1
3、层级：
（1）层级向下调，下调一个层级，少一个单元格，编号少一节，同层级排到最后。
         （2）层级向上调，上调一个层级，继承层级内上一行内容，编号多一节，上调后排在同层级最后；
注意：无法上调细节描述层级，无法超过上一行层级
下调例如1：
☆为当前行位置
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001	LV1	项目管理系统1		/	/
	A002001	LV2	项目管理系统2	移动端		/
	A002002001	LV3	项目管理系统2	Web端	首页	
☆	A002002002001	LV4	项目管理系统2	Web端	模块	新建流程
	A002002002002	LV4	项目管理系统2	Web端	模块	流程台账
	A003	LV1	项目管理系统3		/	/
LV4变成LV3
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001	LV1	项目管理系统1		/	/
	A002001	LV2	项目管理系统2	移动端		/
	A002002001	LV3	项目管理系统2	Web端	首页	
	A002002002001	LV4	项目管理系统2	Web端	模块	流程台账
☆	A002002002	LV3	项目管理系统2	Web端	模块	
	A003	LV1	项目管理系统3		/	/
说明：少细节描述，当前行LV3层级，排A002002002层级最后一个；原LV4的002变成001

LV4变LV2
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001	LV1	项目管理系统1		/	/
	A002001	LV2	项目管理系统2	移动端		/
	A002002001	LV3	项目管理系统2	Web端	首页	
	A002002002001	LV4	项目管理系统2	Web端	模块	流程台账
☆	A002002	LV2	项目管理系统2	Web端		/
	A003	LV1	项目管理系统3		/	/
说明：少细节描述，当前行LV2层级，排A002002层级最后一个；原LV4的002变成001

LV4变LV1
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001	LV1	项目管理系统1		/	/
	A002001	LV2	项目管理系统2	移动端		/
	A002002001	LV3	项目管理系统2	Web端	首页	
	A002002002001	LV4	项目管理系统2	Web端	模块	流程台账
☆	A002	LV1	项目管理系统2		/	/
	A003	LV1	项目管理系统3		/	/
说明：少细节描述，当前行LV2层级，排A002层级最后一个；原LV4的002变成001
下调例如2：
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001	LV1	项目管理系统1		/	/
	A002001	LV2	项目管理系统2	移动端		/
☆	A002002001	LV3	项目管理系统2	Web端	首页	
	A002002002001	LV4	项目管理系统2	Web端	模块	新建流程
	A002002002002	LV4	项目管理系统2	Web端	模块	流程台账
	A003	LV1	项目管理系统3		/	/
LV3变成LV2
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001	LV1	项目管理系统1		/	/
	A002001	LV2	项目管理系统2	移动端		/
	A002002001001	LV4	项目管理系统2	Web端	模块	新建流程
	A002002001002	LV4	项目管理系统2	Web端	模块	流程台账
☆	A002002	LV2	项目管理系统2	Web端		/
	A003	LV1	项目管理系统3		/	/
说明：少了标题3，排A002002层级最后一个；原LV4的三层级002变成001
LV3变成LV1
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001	LV1	项目管理系统1		/	/
	A002001	LV2	项目管理系统2	移动端		/
	A002002001001	LV4	项目管理系统2	Web端	模块	新建流程
	A002002001002	LV4	项目管理系统2	Web端	模块	流程台账
☆	A002	LV1	项目管理系统2		/	/
	A003	LV1	项目管理系统3		/	/
说明：少了标题3、标题2，排A002层级最后一个；原LV4的三层级002变成001
上调例如1：
☆为当前行位置
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001001001001	LV4	项目管理系统1	移动端	首页	个人简介
☆	A002	LV1	项目管理系统2		/	/
LV1调至LV2
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001001001001	LV4	项目管理系统1	移动端	首页	个人简介
☆	A001001	LV2	项目管理系统1	移动端		/
说明：LV2涉及两个标题，当前行标题1、标题2同步上一行标题1标题2，标题3可编辑

上调例如2：
☆为当前行位置
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001001	LV2	项目管理系统1	移动端		/
☆	A002	LV1	项目管理系统2		/	/
LV1调至LV2
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001001	LV2	项目管理系统1	移动端		/
☆	A001001	LV2	项目管理系统1	移动端		/
说明：LV2涉及两个标题，当前行标题1、标题2同步上一行标题1标题2，标题3可编辑
不可上调：
LV1调至LV3
说明：上一行是LV2，LV1可以上调LV2
LV1调至LV4
说明1：上一行是LV2，LV1可以上调LV2
说明2：LV4是细节描述
二、标题内容
仅可修改当前层级内的内容和填写下一级内容，且同层级内容修改同步
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001	LV1	项目管理系统1		/	/
☆	A002001   	LV2	项目管理系统2	移动端	   	/
	A002002001	LV3	项目管理系统2	Web端	首页	
	A002002002001	LV4	项目管理系统2	Web端	模块	新建流程
	A002002002002	LV4	项目管理系统2	Web端	模块	流程台账
	A003	LV1	项目管理系统3		/	/
修改标题1
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001	LV1	项目管理系统1		/	/
☆	A002001	LV2	项目管理X系统	移动端		/
	A002002001	LV3	项目管理X系统	Web端	首页	
	A002002002001	LV4	项目管理X系统	Web端	模块	新建流程
	A002002002002	LV4	项目管理X系统	Web端	模块	流程台账
	A003	LV1	项目管理系统3		/	/
说明：当前行将标题1“项目管理系统2”修改为“项目管理X系统”时，含A002的标题1同步修改
填写标题3
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001	LV1	项目管理系统1		/	/
☆	A002001001	LV3	项目管理系统2	移动端	首页	
	A002002001	LV3	项目管理系统2	Web端	首页	
	A002002002001	LV4	项目管理系统2	Web端	模块	新建流程
	A002002002002	LV4	项目管理系统2	Web端	模块	流程台账
	A003	LV1	项目管理系统3		/	/
说明：填写标题3时，编码第三层从001开始，层级为从LV2变成LV3，下一层级细节描述现在可编辑
三、表格字段新增
操作	编码	层级	标题1	标题2	标题3	细节描述
	A001	LV1	项目管理系统1		/	/
	A002001001	LV3	项目管理系统2	移动端	首页	
	A002002001	LV3	项目管理系统2	Web端	首页	
	A002002002001	LV4	项目管理系统2	Web端	模块	新建流程
	A002002002002	LV4	项目管理系统2	Web端	模块	流程台账
	A003	LV1	项目管理系统3		/	/
例如新增标题4

操作	编码	层级	标题1	标题2	标题3	标题4	细节描述
	A001	LV1	项目管理系统1		/	/	/
	A002001001	LV4	项目管理系统2	移动端	首页	首页	
	A002002001	LV4	项目管理系统2	Web端	首页	首页	
	A002002002001	LV5	项目管理系统2	Web端	模块	模块	新建流程
	A002002002002	LV5	项目管理系统2	Web端	模块	模块	流程台账
	A003	LV1	项目管理系统3		/	/	/
新增标题4，内容与标题3完全一致，原LV3变成LV4，原LV4变成LV5
注意：新增标题为标题最后一列，除标题1和细节描述，其他标题均可删除，删除为整列删除。




 表格功能详解
这份文档核心在于一个具有层级编码和内容管理能力的表格。


✨ 核心字段与功能 

操作 (Operation): 控制表格行的基本行为。

编码 (Encoding): 独有的层级编号系统，严格遵循特定规则。

层级 (Level): 定义了内容的组织结构，从 LV1 到 LV4（或 LV5，取决于是否新增标题）。

标题 (Titles 1-3): 不同层级的内容描述。

细节描述 (Detail Description): 最底层级的详细内容。

🛠️ 操作（Actions）

添加 (Add):

在当前行下方新增一行 。

关键规则:

新增行内容会比当前行少一个单元格 。

编码会减少一节，最后一节编码自动加 1 。

新增行下方的所有行会根据新的排序重新生成编码 。

示例:

在 LV2 行下方新增，结果可能是 LV1 行，且缺少“标题2”内容，编码从两节变为一节，末位加1 。


在 LV4 行下方新增，结果可能是三节编码，缺少“细节描述”内容，末位加1 。



删除 (Delete):

删除当前选中行 。

关键规则:

下方行的编码会根据删除行上一行的编码重新排序生成 。

示例:

删除 LV1 行后，后续同层级及下级行的编码会相应调整，例如“003”变为“002” 。


删除多节编码的行，下方相关编码也会按序调整 。



移动 (Move):

允许在同层级内调整行位置 。

关键规则:

行的最后一节编码会跟随位置调整而变化 。

示例:

将当前行从第一位移动到第二位，其编码的末位会从“001”变为“002”，原“002”变为“001” 。



将当前行移动到第三位，其编码的末位会从“001”变为“003”，其他相关编码也相应调整 。



📊 编码 (Encoding) 规则 


“A”: 代表工程文件或流程图的起点 。


“001”: 代表一个标题或细节描述 。


“A001”: 表示当前行填写了“标题1” 。


“A001001”: 表示当前行填写了“标题1”和“标题2” 。


排序原则: 表格行顺序严格从小到大。相同层级编码加 1；不同层级编码按最少层级加 1 。


🪜 层级 (Level) 调整 


层级向下调整 (Lowering Level):

每向下调整一个层级，会减少一个单元格内容，编码减少一节 。

调整后的行会排在同层级的最后一位 。

示例:

LV4 下调到 LV3，会减少“细节描述”，编码少一节，并排在同层级末位 。


LV4 下调到 LV2 或 LV1，同样遵循减少单元格和编码节数的规则 。


LV3 下调到 LV2，会减少“标题3”，编码少一节，并排在同层级末位 。


LV3 下调到 LV1，会减少“标题3”和“标题2”，编码少两节，并排在同层级末位 。


层级向上调整 (Raising Level):

每向上调整一个层级，会继承上一行层级的内容，编码会增加一节 。

调整后的行会排在同层级的最后一位 。

限制:

无法向上调整“细节描述”的层级 。



无法超过上一行的层级 。



示例:

LV1 向上调整到 LV2，当前行的“标题1”和“标题2”会同步上一行的对应内容，“标题3”可编辑 。




LV1 无法上调到 LV3 或 LV4，因为上一行是 LV2，只能上调到 LV2 。


📝 标题内容 (Title Content) 


编辑范围: 只能修改当前层级内的内容，并填写下一级内容 。


同步修改: 同层级内容修改时，相关联的内容会同步更新 。


示例: 修改“标题1”时，所有包含该“编码”的“标题1”都会同步修改 。



填写下一级: 填写如“标题3”时，编码会增加一节（例如从 LV2 变为 LV3），并且下一级（“细节描述”）会变为可编辑状态 。


➕ 表格字段新增 (Adding Table Fields) 


新增标题列: 新增的标题（例如“标题4”）内容会与前一个标题列完全一致 。


层级变化: 新增标题会导致原有层级向下推移，例如 LV3 变为 LV4，LV4 变为 LV5 。


删除限制: 新增标题会是最后一列标题。除了“标题1”和“细节描述”，其他标题列都可以整列删除 。