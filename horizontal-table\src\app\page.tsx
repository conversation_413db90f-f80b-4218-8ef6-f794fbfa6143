'use client';

import React, { useState } from 'react';
import { CodingEngine, TableRow } from '@/lib/coding-engine-simple';

// 模块4：集成高级行操作的表格组件
function HorizontalTableWithAdvancedOperations() {
  // 使用状态管理数据
  const [rows, setRows] = useState<TableRow[]>([
    {
      id: '1',
      code: 'A001',
      level: 'LV1',
      title1: '项目管理系统1',
      title2: '',
      title3: '',
      description: ''
    },
    {
      id: '2',
      code: 'A002001',
      level: 'LV2',
      title1: '项目管理系统2',
      title2: '移动端',
      title3: '',
      description: ''
    },
    {
      id: '3',
      code: 'A002002001',
      level: 'LV3',
      title1: '项目管理系统2',
      title2: 'Web端',
      title3: '首页',
      description: ''
    },
    {
      id: '4',
      code: 'A002002002001',
      level: 'LV4',
      title1: '项目管理系统2',
      title2: 'Web端',
      title3: '模块',
      description: '新建流程'
    }
  ]);

  // 模块4：行选择状态管理
  const [selectedRowIds, setSelectedRowIds] = useState<string[]>([]);
  const [draggedRowId, setDraggedRowId] = useState<string | null>(null);

  // 添加行功能 - 使用编码引擎
  const handleAddRow = (currentIndex: number) => {
    const currentRow = rows[currentIndex];
    const { code, level } = CodingEngine.generateAddRowCode(currentRow.code, rows);

    const newRow: TableRow = {
      id: `row_${Date.now()}`,
      code,
      level,
      title1: `新项目 ${code}`,
      title2: '',
      title3: '',
      description: ''
    };

    const newRows = [...rows];
    newRows.splice(currentIndex + 1, 0, newRow);
    setRows(newRows);
  };

  // 删除行功能 - 使用编码引擎
  const handleDeleteRow = (index: number) => {
    const newRows = CodingEngine.reorderAfterDelete(index, rows);
    setRows(newRows);
  };

  // 层级上调功能 - 使用模块3增强版
  const handleLevelUp = (index: number) => {
    const result = CodingEngine.levelUpEnhanced(index, rows);
    if (result.success && result.rows) {
      setRows(result.rows);
    } else {
      alert(result.error || '层级上调失败');
    }
  };

  // 层级下调功能 - 使用模块3增强版
  const handleLevelDown = (index: number) => {
    const result = CodingEngine.levelDownEnhanced(index, rows);
    if (result.success && result.rows) {
      setRows(result.rows);
    } else {
      alert(result.error || '层级下调失败');
    }
  };

  // 模块3：内容更新功能 - 支持同层级同步
  const handleContentUpdate = (index: number, field: keyof TableRow, value: string) => {
    const newRows = [...rows];
    newRows[index] = { ...newRows[index], [field]: value };

    // 应用同层级内容同步
    const syncedRows = CodingEngine.syncSameLevelContent(newRows[index], newRows);
    setRows(syncedRows);
  };

  // 模块4：行选择功能 - 优化版
  const handleRowSelect = (rowId: string, isCtrlPressed: boolean = false) => {
    if (isCtrlPressed) {
      // 多选模式：切换选中状态
      setSelectedRowIds(prev =>
        prev.includes(rowId)
          ? prev.filter(id => id !== rowId)
          : [...prev, rowId]
      );
    } else {
      // 单选模式：如果已选中则取消，否则选中
      setSelectedRowIds(prev =>
        prev.includes(rowId) && prev.length === 1
          ? []
          : [rowId]
      );
    }
  };

  // 模块4：复选框选择处理
  const handleCheckboxSelect = (rowId: string, checked: boolean, isCtrlPressed: boolean = false) => {
    if (checked) {
      if (isCtrlPressed || selectedRowIds.length > 0) {
        // 多选模式：添加到选择列表
        setSelectedRowIds(prev => [...prev, rowId]);
      } else {
        // 单选模式
        setSelectedRowIds([rowId]);
      }
    } else {
      // 取消选择
      setSelectedRowIds(prev => prev.filter(id => id !== rowId));
    }
  };

  // 模块4：全选/取消全选
  const handleSelectAll = () => {
    if (selectedRowIds.length === rows.length) {
      setSelectedRowIds([]);
    } else {
      setSelectedRowIds(rows.map(row => row.id));
    }
  };

  // 模块4：智能插入行
  const handleInsertRowAt = (position: number) => {
    const result = CodingEngine.insertRowAt(position, rows);
    if (result.success && result.rows) {
      setRows(result.rows);
    } else {
      alert(result.error || '插入行失败');
    }
  };

  // 模块4：批量删除行
  const handleDeleteSelectedRows = () => {
    if (selectedRowIds.length === 0) {
      alert('请先选择要删除的行');
      return;
    }

    if (confirm(`确定要删除选中的 ${selectedRowIds.length} 行吗？`)) {
      const result = CodingEngine.deleteRows(selectedRowIds, rows);
      if (result.success && result.rows) {
        setRows(result.rows);
        setSelectedRowIds([]);
      } else {
        alert(result.error || '删除失败');
      }
    }
  };

  // 模块4：复制选中行 - 优化版
  const handleCopySelectedRows = (targetPosition: number) => {
    if (selectedRowIds.length === 0) {
      alert('请先选择要复制的行');
      return;
    }

    const result = CodingEngine.copyRowsOptimized(selectedRowIds, targetPosition, rows);
    if (result.success && result.rows) {
      setRows(result.rows);
      // 清空选择状态
      setSelectedRowIds([]);
    } else {
      alert(result.error || '复制失败');
    }
  };

  // 模块4：拖拽功能
  const handleDragStart = (e: React.DragEvent, rowId: string) => {
    setDraggedRowId(rowId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, targetIndex: number) => {
    e.preventDefault();

    if (!draggedRowId) return;

    const draggedIndex = rows.findIndex(row => row.id === draggedRowId);
    if (draggedIndex === -1 || draggedIndex === targetIndex) {
      setDraggedRowId(null);
      return;
    }

    // 移动行
    const result = CodingEngine.moveRows([draggedRowId], targetIndex, rows);
    if (result.success && result.rows) {
      setRows(result.rows);
    }

    setDraggedRowId(null);
  };

  // 获取层级颜色
  const getLevelColor = (level: string) => {
    const colors = {
      'LV1': 'bg-blue-100 text-blue-800 border-blue-200',
      'LV2': 'bg-green-100 text-green-800 border-green-200',
      'LV3': 'bg-purple-100 text-purple-800 border-purple-200',
      'LV4': 'bg-orange-100 text-orange-800 border-orange-200',
      'LV5': 'bg-red-100 text-red-800 border-red-200'
    };
    return colors[level as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  // 模块3：可编辑字段渲染
  const renderEditableField = (
    rowIndex: number,
    field: keyof TableRow,
    value: string,
    enabled: boolean,
    placeholder: string
  ) => {
    if (!enabled) {
      return (
        <div className="px-2 py-1 text-gray-400 text-sm bg-gray-50 rounded">
          /
        </div>
      );
    }

    return (
      <input
        type="text"
        value={value || ''}
        onChange={(e) => handleContentUpdate(rowIndex, field, e.target.value)}
        placeholder={placeholder}
        className="w-full px-2 py-1 text-sm border border-gray-300 rounded min-h-[28px] focus:outline-none focus:ring-1 focus:ring-blue-500"
      />
    );
  };

  // 判断字段是否启用
  const isFieldEnabled = (level: string, field: string) => {
    const levelNum = parseInt(level.substring(2));
    switch (field) {
      case 'title1': return true;
      case 'title2': return levelNum >= 2;
      case 'title3': return levelNum >= 3;
      case 'description': return levelNum >= 4;
      default: return false;
    }
  };

  // 模块4：工具栏组件
  const renderToolbar = () => (
    <div className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* 左侧操作按钮 */}
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleInsertRowAt(0)}
            className="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            ➕ 添加行
          </button>

          <button
            onClick={handleDeleteSelectedRows}
            disabled={selectedRowIds.length === 0}
            className={`px-3 py-2 text-sm font-medium border border-transparent rounded-md transition-colors ${
              selectedRowIds.length > 0
                ? 'text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'
                : 'text-gray-400 bg-gray-200 cursor-not-allowed'
            }`}
          >
            🗑️ 删除选中 ({selectedRowIds.length})
          </button>

          <button
            onClick={() => handleCopySelectedRows(rows.length)}
            disabled={selectedRowIds.length === 0}
            className={`px-3 py-2 text-sm font-medium border border-transparent rounded-md transition-colors ${
              selectedRowIds.length > 0
                ? 'text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
                : 'text-gray-400 bg-gray-200 cursor-not-allowed'
            }`}
          >
            📋 复制选中 ({selectedRowIds.length})
          </button>
        </div>

        {/* 右侧统计信息 */}
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <button
            onClick={handleSelectAll}
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            {selectedRowIds.length === rows.length ? '取消全选' : '全选'}
          </button>
          <span>共 {rows.length} 行，已选 {selectedRowIds.length} 行</span>
        </div>
      </div>
    </div>
  );

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      {/* 工具栏 */}
      {renderToolbar()}

      {/* 表头 */}
      <div className="bg-gray-50 border-b border-gray-200 flex text-sm font-medium text-gray-700">
        <div className="px-4 py-3 border-r border-gray-200 w-12 flex-shrink-0 flex items-center justify-center">
          <input
            type="checkbox"
            checked={selectedRowIds.length === rows.length && rows.length > 0}
            onChange={handleSelectAll}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
          />
        </div>
        <div className="px-4 py-3 border-r border-gray-200 w-32 flex-shrink-0">操作</div>
        <div className="px-4 py-3 border-r border-gray-200 w-32 flex-shrink-0">编码</div>
        <div className="px-4 py-3 border-r border-gray-200 w-20 flex-shrink-0">层级</div>
        <div className="px-4 py-3 border-r border-gray-200 w-48 flex-shrink-0">标题1</div>
        <div className="px-4 py-3 border-r border-gray-200 w-48 flex-shrink-0">标题2</div>
        <div className="px-4 py-3 border-r border-gray-200 w-48 flex-shrink-0">标题3</div>
        <div className="px-4 py-3 flex-1">细节描述</div>
      </div>

      {/* 表格内容 */}
      <div className="divide-y divide-gray-200">
        {rows.map((row, index) => (
          <div
            key={row.id}
            className={`flex hover:bg-gray-50 group cursor-pointer transition-colors ${
              selectedRowIds.includes(row.id) ? 'bg-blue-50 border-blue-200' : ''
            } ${draggedRowId === row.id ? 'opacity-50' : ''}`}
            onClick={(e) => handleRowSelect(row.id, e.ctrlKey)}
            draggable
            onDragStart={(e) => handleDragStart(e, row.id)}
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, index)}
          >
            {/* 选择列 */}
            <div className="px-4 py-3 w-12 border-r border-gray-200 flex-shrink-0 flex items-center justify-center">
              <input
                type="checkbox"
                checked={selectedRowIds.includes(row.id)}
                onChange={(e) => {
                  e.stopPropagation();
                  handleCheckboxSelect(row.id, e.target.checked, e.ctrlKey);
                }}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
            </div>

            {/* 操作列 */}
            <div className="px-4 py-3 w-32 border-r border-gray-200 flex-shrink-0">
              <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddRow(index);
                  }}
                  className="p-1 text-blue-600 hover:bg-blue-50 rounded text-xs"
                  title="添加行"
                >
                  ➕
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleInsertRowAt(index + 1);
                  }}
                  className="p-1 text-purple-600 hover:bg-purple-50 rounded text-xs"
                  title="在此处插入"
                >
                  ⬇️➕
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCopySelectedRows(index + 1);
                  }}
                  className="p-1 text-green-600 hover:bg-green-50 rounded text-xs"
                  title="复制到此处"
                >
                  📋
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteRow(index);
                  }}
                  className="p-1 text-red-600 hover:bg-red-50 rounded text-xs"
                  title="删除"
                >
                  🗑️
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleLevelUp(index);
                  }}
                  className="p-1 text-orange-600 hover:bg-orange-50 rounded text-xs"
                  title="层级上调"
                >
                  ⬆️
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleLevelDown(index);
                  }}
                  className="p-1 text-gray-600 hover:bg-gray-50 rounded text-xs"
                  title="层级下调"
                >
                  ⬇️
                </button>
              </div>
            </div>

            {/* 编码列 */}
            <div className="px-4 py-3 w-32 border-r border-gray-200 flex-shrink-0">
              <span className="font-mono text-sm text-blue-600 font-medium">
                {row.code}
              </span>
            </div>

            {/* 层级列 */}
            <div className="px-4 py-3 w-20 border-r border-gray-200 flex-shrink-0">
              <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded border ${getLevelColor(row.level)}`}>
                {row.level}
              </span>
            </div>

            {/* 标题1列 */}
            <div className="px-4 py-3 w-48 border-r border-gray-200 flex-shrink-0">
              {renderEditableField(index, 'title1', row.title1, isFieldEnabled(row.level, 'title1'), '标题1')}
            </div>

            {/* 标题2列 */}
            <div className="px-4 py-3 w-48 border-r border-gray-200 flex-shrink-0">
              {renderEditableField(index, 'title2', row.title2, isFieldEnabled(row.level, 'title2'), '标题2')}
            </div>

            {/* 标题3列 */}
            <div className="px-4 py-3 w-48 border-r border-gray-200 flex-shrink-0">
              {renderEditableField(index, 'title3', row.title3, isFieldEnabled(row.level, 'title3'), '标题3')}
            </div>

            {/* 细节描述列 */}
            <div className="px-4 py-3 flex-1">
              {renderEditableField(index, 'description', row.description, isFieldEnabled(row.level, 'description'), '细节描述')}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            横向层级表格系统 - 模块化开发
          </h1>
          <p className="text-gray-600">
            模块4：行操作模块 - 批量操作、智能插入、行选择、复制移动
          </p>
        </div>

        <HorizontalTableWithAdvancedOperations />
      </div>
    </div>
  );
}
