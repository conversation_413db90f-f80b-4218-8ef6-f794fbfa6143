/**
 * 表格工具栏组件
 * 提供添加行、添加列等全局操作
 */

'use client';

import React from 'react';

interface TableToolbarProps {
  rowCount: number;
  onAddRow: () => void;
  onAddColumn: () => void;
  canAddColumn: boolean;
}

export function TableToolbar({
  rowCount,
  onAddRow,
  onAddColumn,
  canAddColumn
}: TableToolbarProps) {
  return (
    <div className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* 左侧操作按钮 */}
        <div className="flex items-center gap-2">
          <button
            onClick={onAddRow}
            className="
              inline-flex items-center px-3 py-2 text-sm font-medium
              text-white bg-blue-600 border border-transparent rounded-md
              hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
              transition-colors
            "
            data-testid="toolbar-add-row"
          >
            <span className="mr-2">➕</span>
            添加行
          </button>

          <button
            onClick={onAddColumn}
            disabled={!canAddColumn}
            className={`
              inline-flex items-center px-3 py-2 text-sm font-medium
              border border-transparent rounded-md transition-colors
              ${canAddColumn
                ? 'text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
                : 'text-gray-400 bg-gray-200 cursor-not-allowed'
              }
            `}
            title={canAddColumn ? '添加动态列' : '已达到最大列数'}
            data-testid="toolbar-add-column"
          >
            <span className="mr-2">➕</span>
            添加列
          </button>
        </div>

        {/* 右侧统计信息 */}
        <div className="flex items-center text-sm text-gray-600">
          <span>共 {rowCount} 行</span>
        </div>
      </div>
    </div>
  );
}

// 默认导出
export default TableToolbar;
